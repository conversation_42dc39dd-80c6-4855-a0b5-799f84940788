const db = require('../utils/db');

class LinkDeviceAssignment {
  // 创建链接与设备的关联
  static async create(linkId, deviceId) {
    const sql = 'INSERT INTO link_device_assignments (link_id, device_id, status, assigned_at) VALUES (?, ?, "processing", NOW())';
    const [result] = await db.query(sql, [linkId, deviceId]);
    return result.insertId;
  }

  // 获取链接的所有活跃分配
  static async getActiveAssignments(linkId) {
    const sql = 'SELECT * FROM link_device_assignments WHERE link_id = ? AND status = "processing"';
    const [results] = await db.query(sql, [linkId]);
    return results;
  }

  // 获取设备的活跃分配
  static async getDeviceActiveAssignment(deviceId) {
    const sql = 'SELECT lda.*, l.url, l.task_id FROM link_device_assignments lda JOIN links l ON lda.link_id = l.id WHERE lda.device_id = ? AND lda.status = "processing" LIMIT 1';
    const [results] = await db.query(sql, [deviceId]);
    return results.length > 0 ? results[0] : null;
  }

  // 更新分配状态
  static async updateStatus(linkId, deviceId, status) {
    const sql = 'UPDATE link_device_assignments SET status = ?, updated_at = NOW() WHERE link_id = ? AND device_id = ? AND status = "processing"';
    const [result] = await db.query(sql, [status, linkId, deviceId]);
    return result;
  }

  // 检查并重置超时的分配
  static async checkAndResetTimeouts(userId, timeoutSeconds) {
    // 获取用户的所有设备ID
    const deviceSql = 'SELECT id FROM devices WHERE user_id = ?';
    const [devices] = await db.query(deviceSql, [userId]);
    const deviceIds = devices.map(d => d.id);
    
    if (deviceIds.length === 0) {
      return 0;
    }

    // 重置超时的分配 - 使用拼接的问号占位符而不是直接传递数组
    const placeholders = deviceIds.map(() => '?').join(',');
    const sql = `
      UPDATE link_device_assignments lda
      JOIN links l ON lda.link_id = l.id
      SET lda.status = "timeout", lda.updated_at = NOW()
      WHERE lda.device_id IN (${placeholders}) 
      AND lda.status = "processing"
      AND TIMESTAMPDIFF(SECOND, lda.assigned_at, NOW()) > ?
    `;
    
    // 展开设备ID数组，并添加超时秒数作为最后一个参数
    const params = [...deviceIds, timeoutSeconds];
    const [result] = await db.query(sql, params);
    return result.affectedRows;
  }

  // 计算链接当前的活跃分配数量
  static async countActiveAssignments(linkId) {
    const sql = 'SELECT COUNT(*) as count FROM link_device_assignments WHERE link_id = ? AND status = "processing"';
    const [results] = await db.query(sql, [linkId]);
    return results[0].count;
  }
}

module.exports = LinkDeviceAssignment; 