const db = require('../utils/db');

class Task {
  // 根据ID获取任务
  static async getById(id) {
    const sql = 'SELECT * FROM tasks WHERE id = ?';
    const [results] = await db.query(sql, [id]);
    return results.length ? results[0] : null;
  }

  // 获取用户的所有任务
  static async getAllByUserId(userId, filters = {}, limit = null, offset = null) {
    let sql = 'SELECT * FROM tasks WHERE user_id = ?';
    const params = [userId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.priority) {
      sql += ' AND priority = ?';
      params.push(filters.priority);
    }

    // 排序
    sql += ' ORDER BY ';
    if (filters.orderBy) {
      sql += `${filters.orderBy} ${filters.orderDir || 'DESC'}`;
    } else {
      sql += 'created_at DESC';
    }
    
    // 添加分页
    if (limit !== null) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset !== null) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const [results] = await db.query(sql, params);
    return results;
  }

  // 计算用户任务总数（用于分页）
  static async countByUserId(userId, filters = {}) {
    let sql = 'SELECT COUNT(*) as count FROM tasks WHERE user_id = ?';
    const params = [userId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.priority) {
      sql += ' AND priority = ?';
      params.push(filters.priority);
    }

    const [results] = await db.query(sql, params);
    return results[0].count;
  }

  // 创建任务
  static async create(taskData) {
    const { 
      user_id, name, description, status, priority, target_likes, target_collects, target_comments 
    } = taskData;
    
    const sql = `
      INSERT INTO tasks 
      (user_id, name, description, status, priority, target_likes, target_collects, target_comments) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      user_id, name, description, status || 'active', priority || 'medium', 
      target_likes || 0, target_collects || 0, target_comments || 0
    ]);
    
    return result.insertId;
  }

  // 更新任务
  static async update(id, taskData) {
    const fields = [];
    const values = [];

    // 动态构建更新字段
    for (const [key, value] of Object.entries(taskData)) {
      if (value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (fields.length === 0) return null;

    values.push(id);
    const sql = `UPDATE tasks SET ${fields.join(', ')} WHERE id = ?`;
    
    const [result] = await db.query(sql, values);
    return result;
  }

  // 删除任务
  static async delete(id) {
    const sql = 'DELETE FROM tasks WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 获取任务中的链接数量
  static async getLinkCount(taskId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ?';
    const [results] = await db.query(sql, [taskId]);
    return results[0].count;
  }

  // 获取任务中的活跃链接数量
  static async getActiveLinkCount(taskId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ? AND status = "active"';
    const [results] = await db.query(sql, [taskId]);
    return results[0].count;
  }

  // 获取任务中的已完成链接数量
  static async getCompletedLinkCount(taskId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ? AND status = "completed"';
    const [results] = await db.query(sql, [taskId]);
    return results[0].count;
  }

  // 获取任务中的错误链接数量
  static async getErrorLinkCount(taskId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ? AND status = "error"';
    const [results] = await db.query(sql, [taskId]);
    return results[0].count;
  }

  // 获取任务的进度
  static async getProgress(taskId) {
    const totalCount = await this.getLinkCount(taskId);
    if (totalCount === 0) return 0;
    
    const completedCount = await this.getCompletedLinkCount(taskId);
    return Math.round((completedCount / totalCount) * 100);
  }
  
  // 更新任务的实际操作数量
  static async updateActualCounts(taskId) {
    // 获取任务中所有链接的点赞、收藏和评论操作总数
    const sql = `
      SELECT 
        SUM(like_count) as total_likes, 
        SUM(collect_count) as total_collects,
        SUM(comment_count) as total_comments
      FROM links 
      WHERE task_id = ?
    `;
    
    const [results] = await db.query(sql, [taskId]);
    const { total_likes, total_collects, total_comments } = results[0];
    
    // 更新任务的实际操作数
    const updateSql = `
      UPDATE tasks 
      SET actual_likes = ?, actual_collects = ?, actual_comments = ? 
      WHERE id = ?
    `;
    
    const [result] = await db.query(updateSql, [
      total_likes || 0, 
      total_collects || 0,
      total_comments || 0,
      taskId
    ]);
    
    return result;
  }
  
  // 增加任务的实际点赞数
  static async incrementActualLikes(taskId, count = 1) {
    const sql = 'UPDATE tasks SET actual_likes = actual_likes + ? WHERE id = ?';
    const [result] = await db.query(sql, [count, taskId]);
    return result;
  }
  
  // 增加任务的实际收藏数
  static async incrementActualCollects(taskId, count = 1) {
    const sql = 'UPDATE tasks SET actual_collects = actual_collects + ? WHERE id = ?';
    const [result] = await db.query(sql, [count, taskId]);
    return result;
  }
  
  // 增加任务的实际评论数
  static async incrementActualComments(taskId, count = 1) {
    const sql = 'UPDATE tasks SET actual_comments = actual_comments + ? WHERE id = ?';
    const [result] = await db.query(sql, [count, taskId]);
    return result;
  }

  // 获取处理中链接数量
  static async getProcessingLinkCount(taskId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ? AND status = "processing"';
    const [results] = await db.query(sql, [taskId]);
    return results[0].count;
  }

  /**
   * 获取任务进度详情
   * @param {number} taskId 任务ID
   * @returns {Promise<object>} 任务进度详情
   */
  static async getTaskProgress(taskId) {
    try {
      // 获取链接状态统计
      const linkStatusQuery = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
          SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
          SUM(CASE WHEN status = 'timeout' THEN 1 ELSE 0 END) as timeout
        FROM links
        WHERE task_id = ?
      `;
      
      const [linkStatus] = await db.query(linkStatusQuery, [taskId]);
      
      // 计算进度百分比
      const percentage = linkStatus.total > 0 
        ? (linkStatus.completed / linkStatus.total * 100) 
        : 0;
      
      // 获取操作结果统计
      const operationResultsQuery = `
        SELECT 
          SUM(CASE WHEN operation_type = 'like' AND status = 'success' THEN 1 ELSE 0 END) as likeSuccess,
          SUM(CASE WHEN operation_type = 'collect' AND status = 'success' THEN 1 ELSE 0 END) as collectSuccess,
          SUM(CASE WHEN operation_type = 'like' AND status = 'failed' THEN 1 ELSE 0 END) as likeFailed,
          SUM(CASE WHEN operation_type = 'collect' AND status = 'failed' THEN 1 ELSE 0 END) as collectFailed
        FROM operation_logs
        WHERE task_id = ?
      `;
      
      const [operationResults] = await db.query(operationResultsQuery, [taskId]);
      
      // 获取近期操作日志
      const recentLogsQuery = `
        SELECT 
          ol.*,
          d.device_name,
          l.url
        FROM operation_logs ol
        LEFT JOIN devices d ON ol.device_id = d.id
        LEFT JOIN links l ON ol.link_id = l.id
        WHERE ol.task_id = ?
        ORDER BY ol.created_at DESC
        LIMIT 10
      `;
      
      const recentLogs = await db.query(recentLogsQuery, [taskId]);
      
      // 获取参与该任务的设备信息
      const task = await this.getById(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      const devicesQuery = `
        SELECT 
          d.id,
          d.device_name,
          d.last_active_time,
          CASE WHEN TIMESTAMPDIFF(MINUTE, d.last_active_time, NOW()) <= 5 THEN 1 ELSE 0 END as is_online,
          COUNT(DISTINCT ol.id) as total_operations,
          SUM(CASE WHEN ol.status = 'success' THEN 1 ELSE 0 END) as success_count,
          SUM(CASE WHEN ol.status = 'failed' THEN 1 ELSE 0 END) as failed_count
        FROM devices d
        LEFT JOIN operation_logs ol ON d.id = ol.device_id AND ol.task_id = ?
        WHERE d.user_id = ?
        GROUP BY d.id
        HAVING total_operations > 0
        ORDER BY is_online DESC, total_operations DESC
        LIMIT 10
      `;
      
      const devices = await db.query(devicesQuery, [taskId, task.user_id]);
      
      return {
        taskId,
        taskName: task.name,
        status: task.status,
        progressData: {
          ...linkStatus,
          percentage
        },
        operationResults: {
          likeSuccess: operationResults.likeSuccess || 0,
          collectSuccess: operationResults.collectSuccess || 0,
          likeFailed: operationResults.likeFailed || 0,
          collectFailed: operationResults.collectFailed || 0
        },
        recentLogs,
        devices
      };
    } catch (error) {
      console.error('获取任务进度详情出错:', error);
      throw error;
    }
  }
}

module.exports = Task; 