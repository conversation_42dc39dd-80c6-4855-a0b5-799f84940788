const db = require('../utils/db');

class User {
  // 根据token获取用户
  static async getByToken(token) {
    const sql = 'SELECT * FROM users WHERE token = ? AND status = "active"';
    const [results] = await db.query(sql, [token]);
    return results.length ? results[0] : null;
  }

  // 根据用户名获取用户
  static async getByUsername(username) {
    const sql = 'SELECT * FROM users WHERE username = ? AND status = "active"';
    const [results] = await db.query(sql, [username]);
    return results.length ? results[0] : null;
  }

  // 根据用户名和密码获取用户
  static async getByUsernameAndToken(username, token) {
    const sql = 'SELECT * FROM users WHERE username = ? AND token = ? AND status = "active"';
    const [results] = await db.query(sql, [username, token]);
    return results.length ? results[0] : null;
  }

  // 根据ID获取用户
  static async getById(id) {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const [results] = await db.query(sql, [id]);
    return results.length ? results[0] : null;
  }

  // 获取所有用户
  static async getAll() {
    const sql = 'SELECT * FROM users ORDER BY id DESC';
    const [results] = await db.query(sql);
    return results;
  }

  // 创建用户
  static async create(userData) {
    const { username, token } = userData;
    const sql = 'INSERT INTO users (username, token) VALUES (?, ?)';
    const [result] = await db.query(sql, [username, token]);
    return result.insertId;
  }

  // 更新用户
  static async update(id, userData) {
    const { username, token, status } = userData;
    const sql = 'UPDATE users SET username = ?, token = ?, status = ? WHERE id = ?';
    const [result] = await db.query(sql, [username, token, status, id]);
    return result;
  }

  // 删除用户
  static async delete(id) {
    const sql = 'DELETE FROM users WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 获取用户设备数量
  static async getDeviceCount(userId) {
    const sql = 'SELECT COUNT(*) as count FROM devices WHERE user_id = ?';
    const [results] = await db.query(sql, [userId]);
    return results[0].count;
  }

  // 获取用户链接数量
  static async getLinkCount(userId) {
    const sql = 'SELECT COUNT(*) as count FROM links WHERE user_id = ?';
    const [results] = await db.query(sql, [userId]);
    return results[0].count;
  }
}

module.exports = User; 