const db = require('../utils/db');

class OperationLog {
  // 创建操作日志
  static async create(logData) {
    const { 
      user_id, device_id, link_id, task_id, operation_type, 
      status, like_count, collect_count, error_message 
    } = logData;
    
    // 确保operation_type是有效的枚举值
    let safeOperationType = operation_type;
    if (operation_type === 'both') {
      // 如果是both，则使用like作为默认值，因为数据库可能不接受both
      safeOperationType = 'like';
      
      // 记录日志
      console.log(`操作类型转换: 将"both"转换为"${safeOperationType}"`);
      
      // 如果需要，可以创建两条记录，一条like一条collect
      if (collect_count > 0) {
        // 先创建collect记录
        const collectSql = `
          INSERT INTO operation_logs 
          (user_id, device_id, link_id, task_id, operation_type, status, like_count, collect_count, error_message) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        await db.query(collectSql, [
          user_id, device_id, link_id, task_id || null, 'collect', 
          status, 0, collect_count || 0, error_message || ''
        ]);
      }
    }
    
    const sql = `
      INSERT INTO operation_logs 
      (user_id, device_id, link_id, task_id, operation_type, status, like_count, collect_count, error_message) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      user_id, device_id, link_id, task_id || null, safeOperationType, 
      status, like_count || 0, operation_type === 'both' ? 0 : (collect_count || 0), error_message || ''
    ]);
    
    return result.insertId;
  }

  // 获取用户的操作日志
  static async getByUserId(userId, limit = 100) {
    const sql = `
      SELECT ol.*, l.url, d.device_name
      FROM operation_logs ol
      JOIN links l ON ol.link_id = l.id
      JOIN devices d ON ol.device_id = d.id
      WHERE ol.user_id = ?
      ORDER BY ol.created_at DESC
      LIMIT ?
    `;
    
    const [results] = await db.query(sql, [userId, limit]);
    return results;
  }

  // 获取设备的操作日志
  static async getByDeviceId(deviceId, limit = 100) {
    const sql = `
      SELECT ol.*, l.url
      FROM operation_logs ol
      JOIN links l ON ol.link_id = l.id
      WHERE ol.device_id = ?
      ORDER BY ol.created_at DESC
      LIMIT ?
    `;
    
    const [results] = await db.query(sql, [deviceId, limit]);
    return results;
  }

  // 获取链接的操作日志
  static async getByLinkId(linkId, limit = 100) {
    const sql = `
      SELECT ol.*, d.device_name
      FROM operation_logs ol
      JOIN devices d ON ol.device_id = d.id
      WHERE ol.link_id = ?
      ORDER BY ol.created_at DESC
      LIMIT ?
    `;
    
    const [results] = await db.query(sql, [linkId, limit]);
    return results;
  }

  // 获取今日操作统计
  static async getTodayStats(userId) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
        SUM(like_count) as likes,
        SUM(collect_count) as collects
      FROM operation_logs
      WHERE user_id = ? AND DATE(created_at) = CURDATE()
    `;
    
    const [results] = await db.query(sql, [userId]);
    return results[0];
  }

  // 获取按日期分组的操作统计
  static async getDailyStats(userId, days = 7, startDate = null, endDate = null) {
    let sql = `
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN operation_type = 'like' THEN 1 ELSE 0 END) as likes,
        SUM(CASE WHEN operation_type = 'collect' THEN 1 ELSE 0 END) as collects
      FROM operation_logs
      WHERE user_id = ?
    `;

    const params = [userId];

    if (startDate && endDate) {
      // 使用自定义日期范围
      sql += ` AND DATE(created_at) >= ? AND DATE(created_at) <= ?`;
      params.push(startDate, endDate);
    } else {
      // 使用天数范围
      sql += ` AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)`;
      params.push(days);
    }

    sql += `
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const [results] = await db.query(sql, params);
    return results;
  }
  
  // 获取设备的操作计数（当天）
  static async getDeviceOperationCount(deviceId) {
    const sql = `
      SELECT COUNT(*) as count
      FROM operation_logs
      WHERE device_id = ? AND DATE(created_at) = CURDATE() AND status = 'success'
    `;
    
    const [results] = await db.query(sql, [deviceId]);
    return results[0].count || 0;
  }
  
  // 获取设备的成功率
  static async getDeviceSuccessRate(deviceId) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success
      FROM operation_logs 
      WHERE device_id = ? AND DATE(created_at) = CURDATE()
    `;
    const [results] = await db.query(sql, [deviceId]);
    if (results[0].total === 0) return 0;
    return (results[0].success / results[0].total) * 100;
  }

  // 检查设备是否已经成功操作过特定链接
  static async checkDeviceOperatedLink(deviceId, linkId, operationType) {
    let sql = `
      SELECT COUNT(*) as count
      FROM operation_logs
      WHERE device_id = ? AND link_id = ?
    `;

    // 如果是both操作，需要检查是否执行过like或collect
    if (operationType === 'both') {
      sql += ` AND (operation_type = 'like' OR operation_type = 'collect')`;
    } else {
      sql += ` AND operation_type = ?`;
    }

    const params = operationType === 'both' ? [deviceId, linkId] : [deviceId, linkId, operationType];
    const [results] = await db.query(sql, params);

    return results[0].count > 0;
  }

  // 获取总计统计数据
  static async getTotalStats(userId) {
    try {
      const sql = `
        SELECT
          COUNT(*) as total_operations,
          SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_operations,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_operations,
          SUM(CASE WHEN operation_type = 'like' THEN 1 ELSE 0 END) as total_likes,
          SUM(CASE WHEN operation_type = 'collect' THEN 1 ELSE 0 END) as total_collects,
          COUNT(DISTINCT device_id) as active_devices,
          COUNT(DISTINCT link_id) as operated_links
        FROM operation_logs
        WHERE user_id = ?
      `;

      const [results] = await db.query(sql, [userId]);
      const result = results[0] || {};

      // 确保所有字段都有默认值
      return {
        total_operations: result.total_operations || 0,
        success_operations: result.success_operations || 0,
        failed_operations: result.failed_operations || 0,
        total_likes: result.total_likes || 0,
        total_collects: result.total_collects || 0,
        active_devices: result.active_devices || 0,
        operated_links: result.operated_links || 0
      };
    } catch (error) {
      console.error('获取总计统计错误:', error);
      return {
        total_operations: 0,
        success_operations: 0,
        failed_operations: 0,
        total_likes: 0,
        total_collects: 0,
        active_devices: 0,
        operated_links: 0
      };
    }
  }

  // 获取效率统计数据
  static async getEfficiencyStats(userId, days = 7) {
    try {
      const sql = `
        SELECT
          AVG(CASE WHEN status = 'success' THEN 1 ELSE 0 END) * 100 as avg_success_rate,
          COUNT(*) / ? as avg_operations_per_day,
          COUNT(DISTINCT DATE(created_at)) as active_days,
          MAX(DATE(created_at)) as last_active_date,
          MIN(DATE(created_at)) as first_active_date
        FROM operation_logs
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      `;

      const [results] = await db.query(sql, [days, userId, days]);
      const result = results[0] || {};

      // 确保所有字段都有默认值并且是正确的数据类型
      return {
        avg_success_rate: parseFloat(result.avg_success_rate) || 0,
        avg_operations_per_day: parseFloat(result.avg_operations_per_day) || 0,
        active_days: parseInt(result.active_days) || 0,
        last_active_date: result.last_active_date || null,
        first_active_date: result.first_active_date || null
      };
    } catch (error) {
      console.error('获取效率统计错误:', error);
      return {
        avg_success_rate: 0,
        avg_operations_per_day: 0,
        active_days: 0,
        last_active_date: null,
        first_active_date: null
      };
    }
  }
}

module.exports = OperationLog; 