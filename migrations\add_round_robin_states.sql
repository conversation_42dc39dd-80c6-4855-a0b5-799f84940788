-- 创建循环分配状态表
CREATE TABLE IF NOT EXISTS `round_robin_states` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `high_priority_counter` int(11) DEFAULT 0 COMMENT '高优先级计数器',
  `medium_priority_counter` int(11) DEFAULT 0 COMMENT '中优先级计数器', 
  `low_priority_counter` int(11) DEFAULT 0 COMMENT '低优先级计数器',
  `last_reset_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上次重置时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='循环分配状态表';

-- 为现有用户初始化循环分配状态
INSERT INTO `round_robin_states` (`user_id`, `high_priority_counter`, `medium_priority_counter`, `low_priority_counter`, `last_reset_time`)
SELECT `id`, 0, 0, 0, NOW()
FROM `users`
WHERE `id` NOT IN (SELECT `user_id` FROM `round_robin_states`);

-- 添加新的设置项到现有用户
INSERT INTO `settings` (`user_id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`)
SELECT u.id, 'link.allocation_mode', 'smart', '链接分配模式', NOW(), NOW()
FROM `users` u
WHERE NOT EXISTS (
    SELECT 1 FROM `settings` s 
    WHERE s.user_id = u.id AND s.setting_key = 'link.allocation_mode'
);

INSERT INTO `settings` (`user_id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`)
SELECT u.id, 'link.round_robin_reset_hours', '24', '循环计数器重置间隔（小时）', NOW(), NOW()
FROM `users` u
WHERE NOT EXISTS (
    SELECT 1 FROM `settings` s 
    WHERE s.user_id = u.id AND s.setting_key = 'link.round_robin_reset_hours'
);

INSERT INTO `settings` (`user_id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`)
SELECT u.id, 'link.priority_strict_mode', 'true', '严格优先级模式', NOW(), NOW()
FROM `users` u
WHERE NOT EXISTS (
    SELECT 1 FROM `settings` s 
    WHERE s.user_id = u.id AND s.setting_key = 'link.priority_strict_mode'
);
