-- MySQL dump 10.13  Distrib 5.7.26, for Win64 (x86_64)
--
-- Host: localhost    Database: xhs666
-- ------------------------------------------------------
-- Server version	5.7.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `device_configs`
--

DROP TABLE IF EXISTS `device_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `device_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `config` json NOT NULL COMMENT '设备配置(JSON格式)',
  `description` text COMMENT '配置描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认配置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_configs`
--

LOCK TABLES `device_configs` WRITE;
/*!40000 ALTER TABLE `device_configs` DISABLE KEYS */;
INSERT INTO `device_configs` VALUES (2,1,'操作几次恢复出厂设置','{\"操作几次恢复出厂设置\": 4}','还原几次备份操作后恢复出厂设置',1,'2025-07-04 10:28:17','2025-07-04 21:59:42'),(3,1,'sdgfsdfs','{\"aaaaa\": 4}','ffffffffffffff',0,'2025-07-04 16:58:43','2025-07-04 21:59:42');
/*!40000 ALTER TABLE `device_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_token` varchar(100) NOT NULL,
  `last_active_time` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES (14,1,'测试设备','device_1751568476736_ten3izch','2025-07-04 21:59:46','active','2025-07-04 19:05:26','2025-07-04 21:59:46'),(2,1,'测试设备','device_1751562383084_4ys0xk6s','2025-07-03 17:29:27','active','2025-07-03 17:06:25','2025-07-03 17:29:27'),(3,1,'测试设备','device_1751563773213_tyk6roi8','2025-07-03 17:29:58','active','2025-07-03 17:29:35','2025-07-03 17:29:58'),(4,1,'测试设备','device_1751563835029_e607lkqz','2025-07-03 17:33:07','active','2025-07-03 17:30:36','2025-07-03 17:33:07'),(5,1,'测试设备','device_1751564805557_bcd12k33','2025-07-03 17:47:26','active','2025-07-03 17:46:47','2025-07-03 17:47:26'),(6,1,'测试设备','device_1751564862477_co2r90zt','2025-07-03 17:47:55','active','2025-07-03 17:47:43','2025-07-03 17:47:55'),(7,1,'测试设备','device_1751564917542_qj80cqjc','2025-07-03 17:48:51','active','2025-07-03 17:48:39','2025-07-03 17:48:51'),(8,1,'测试设备','device_1751564950886_j438qv3j','2025-07-03 17:49:28','active','2025-07-03 17:49:12','2025-07-03 17:49:28'),(9,1,'测试设备','device_1751564946278_4kp1ullv','2025-07-03 17:49:36','active','2025-07-03 17:49:15','2025-07-03 17:49:36'),(10,1,'测试设备','device_1751565216414_5rk1o9s8','2025-07-03 17:54:05','active','2025-07-03 17:53:37','2025-07-03 17:54:05'),(11,1,'测试设备','device_1751565223829_ruxhv5fj','2025-07-03 17:57:09','active','2025-07-03 17:53:44','2025-07-03 17:57:09'),(12,1,'测试设备','device_1751565433661_g2m1ntwm','2025-07-03 17:59:03','active','2025-07-03 17:57:14','2025-07-03 17:59:03'),(13,1,'测试设备','device_1751565466830_7o3edavz','2025-07-03 17:58:48','active','2025-07-03 17:57:47','2025-07-03 17:58:48');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `link_device_assignments`
--

DROP TABLE IF EXISTS `link_device_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `link_device_assignments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `link_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `status` enum('processing','completed','failed','timeout') NOT NULL DEFAULT 'processing',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `link_id` (`link_id`),
  KEY `device_id` (`device_id`)
) ENGINE=MyISAM AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `link_device_assignments`
--

LOCK TABLES `link_device_assignments` WRITE;
/*!40000 ALTER TABLE `link_device_assignments` DISABLE KEYS */;
INSERT INTO `link_device_assignments` VALUES (1,59,2,'completed','2025-07-03 17:28:28','2025-07-03 17:29:03'),(2,59,4,'completed','2025-07-03 17:32:45','2025-07-03 17:32:52'),(3,60,4,'completed','2025-07-03 17:33:04','2025-07-03 17:33:07'),(4,69,5,'completed','2025-07-03 17:46:48','2025-07-03 17:47:07'),(5,68,6,'completed','2025-07-03 17:47:46','2025-07-03 17:47:55'),(6,69,7,'completed','2025-07-03 17:48:40','2025-07-03 17:48:51'),(7,68,9,'completed','2025-07-03 17:49:17','2025-07-03 17:49:36'),(8,68,8,'completed','2025-07-03 17:49:21','2025-07-03 17:49:28'),(9,69,10,'completed','2025-07-03 17:53:39','2025-07-03 17:54:05'),(10,69,11,'completed','2025-07-03 17:53:46','2025-07-03 17:53:55'),(11,68,11,'completed','2025-07-03 17:54:27','2025-07-03 17:54:31'),(12,69,12,'completed','2025-07-03 17:57:16','2025-07-03 17:57:30'),(13,68,13,'completed','2025-07-03 17:57:49','2025-07-03 17:58:01'),(14,67,12,'completed','2025-07-03 17:58:27','2025-07-03 17:59:03'),(15,69,13,'completed','2025-07-03 17:58:33','2025-07-03 17:58:37'),(16,67,13,'completed','2025-07-03 17:58:39','2025-07-03 17:58:48'),(17,69,14,'completed','2025-07-04 19:55:30','2025-07-04 19:55:35'),(18,68,14,'processing','2025-07-04 21:59:46','2025-07-04 21:59:46');
/*!40000 ALTER TABLE `link_device_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `links`
--

DROP TABLE IF EXISTS `links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_id` int(11) DEFAULT NULL COMMENT '关联的任务ID',
  `url` varchar(255) NOT NULL,
  `original_likes` int(11) DEFAULT '0',
  `original_likes_set_time` timestamp NULL DEFAULT NULL COMMENT '初始点赞数设置时间',
  `current_likes` int(11) DEFAULT '0',
  `original_collects` int(11) DEFAULT '0',
  `original_collects_set_time` timestamp NULL DEFAULT NULL COMMENT '初始收藏数设置时间',
  `original_comments` int(11) DEFAULT '0',
  `current_collects` int(11) DEFAULT '0',
  `current_comments` int(11) DEFAULT '0',
  `like_count` int(11) DEFAULT '0',
  `like_operations` int(11) NOT NULL DEFAULT '0' COMMENT '点赞操作总次数（包括成功和失败）',
  `collect_count` int(11) DEFAULT '0',
  `collect_operations` int(11) NOT NULL DEFAULT '0' COMMENT '收藏操作总次数（包括成功和失败）',
  `comment_count` int(11) DEFAULT '0',
  `target_likes` int(11) DEFAULT '0',
  `target_collects` int(11) DEFAULT '0',
  `target_comments` int(11) DEFAULT '0',
  `fail_count` int(11) DEFAULT '0',
  `priority` enum('high','medium','low') DEFAULT 'medium',
  `status` enum('active','paused','completed','error','processing') DEFAULT 'active',
  `batch` varchar(100) DEFAULT NULL COMMENT '批次名称',
  `note` text,
  `last_operation_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `device_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_batch` (`batch`),
  KEY `idx_status_priority` (`status`,`priority`),
  KEY `idx_last_operation_time` (`last_operation_time`)
) ENGINE=MyISAM AUTO_INCREMENT=71 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `links`
--

LOCK TABLES `links` WRITE;
/*!40000 ALTER TABLE `links` DISABLE KEYS */;
INSERT INTO `links` VALUES (69,1,35,'http://xhslink.com/a/rjyX5sjk6ARfb',3,'2025-07-03 17:47:07',4,0,'2025-07-04 19:55:35',0,0,0,7,7,0,0,0,15,0,0,0,'medium','active',NULL,NULL,'2025-07-04 19:55:35','2025-07-03 17:46:25','2025-07-04 19:55:35',NULL),(68,1,35,'http://xhslink.com/a/ilVCxOC4yzMfb',2,'2025-07-03 17:49:36',5,0,'2025-07-03 17:58:01',0,0,0,5,5,0,0,0,15,0,0,0,'medium','active',NULL,NULL,'2025-07-03 17:58:01','2025-07-03 17:46:25','2025-07-03 17:58:01',NULL),(67,1,35,'http://xhslink.com/a/W8N6PpHBvzMfb',5,'2025-07-03 17:59:03',6,0,'2025-07-03 17:59:03',0,0,0,2,2,0,0,0,15,0,0,0,'medium','active',NULL,NULL,'2025-07-03 17:59:03','2025-07-03 17:46:25','2025-07-03 17:59:03',NULL),(66,1,35,'http://xhslink.com/a/uGNS8M5tozMfb',0,NULL,0,0,NULL,0,0,0,0,0,0,0,0,15,0,0,0,'medium','active',NULL,NULL,NULL,'2025-07-03 17:46:25','2025-07-03 17:46:25',NULL),(65,1,35,'http://xhslink.com/a/mlMsTydokzMfb',0,NULL,0,0,NULL,0,0,0,0,0,0,0,0,15,0,0,0,'medium','active',NULL,NULL,NULL,'2025-07-03 17:46:25','2025-07-03 17:46:25',NULL),(64,1,35,'http://xhslink.com/a/DCluzaBVxVRfb',0,NULL,0,0,NULL,0,0,0,0,0,0,0,0,15,0,0,0,'medium','active',NULL,NULL,NULL,'2025-07-03 17:46:25','2025-07-03 17:46:25',NULL),(70,1,35,'http://xhslink.com/a/WzgqEEOZyIRfb',0,NULL,0,0,NULL,0,0,0,0,0,0,0,0,15,0,0,0,'medium','active',NULL,NULL,NULL,'2025-07-03 17:46:25','2025-07-03 17:46:25',NULL);
/*!40000 ALTER TABLE `links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_logs`
--

DROP TABLE IF EXISTS `operation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `link_id` int(11) NOT NULL,
  `task_id` int(11) DEFAULT NULL COMMENT '关联的任务ID',
  `operation_type` enum('like','collect','comment','view','other') NOT NULL,
  `status` enum('success','fail') NOT NULL,
  `likes_count` int(11) DEFAULT NULL,
  `collects_count` int(11) DEFAULT NULL,
  `comments_count` int(11) DEFAULT NULL,
  `error_message` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `like_count` int(11) DEFAULT '0',
  `collect_count` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `device_id` (`device_id`),
  KEY `link_id` (`link_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=MyISAM AUTO_INCREMENT=21 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_logs`
--

LOCK TABLES `operation_logs` WRITE;
/*!40000 ALTER TABLE `operation_logs` DISABLE KEYS */;
INSERT INTO `operation_logs` VALUES (1,1,1,38,5,'collect','success',1,1,NULL,'','2025-07-03 09:56:29',0,0),(2,1,1,50,8,'like','success',NULL,NULL,NULL,'','2025-07-03 10:31:06',1,1),(3,1,1,51,8,'like','success',NULL,NULL,NULL,'','2025-07-03 10:31:54',1,1),(4,1,2,59,34,'like','success',NULL,NULL,NULL,'','2025-07-03 17:29:03',1,0),(5,1,4,59,34,'like','success',NULL,NULL,NULL,'','2025-07-03 17:32:52',1,0),(6,1,4,60,34,'like','success',NULL,NULL,NULL,'','2025-07-03 17:33:07',1,0),(7,1,5,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:47:07',1,0),(8,1,6,68,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:47:55',1,0),(9,1,7,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:48:51',1,0),(10,1,8,68,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:49:28',1,0),(11,1,9,68,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:49:36',1,0),(12,1,11,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:53:55',1,0),(13,1,10,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:54:05',1,0),(14,1,11,68,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:54:31',1,0),(15,1,12,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:57:30',1,0),(16,1,13,68,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:58:01',1,0),(17,1,13,69,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:58:37',1,0),(18,1,13,67,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:58:48',1,0),(19,1,12,67,35,'like','success',NULL,NULL,NULL,'','2025-07-03 17:59:03',1,0),(20,1,14,69,35,'like','success',NULL,NULL,NULL,'','2025-07-04 19:55:35',1,0);
/*!40000 ALTER TABLE `operation_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `description` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_setting` (`user_id`,`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES (7,1,'device.auto_assign','true','自动分配任务给设备','2025-07-03 00:38:36','2025-07-03 00:38:36'),(8,1,'device.max_tasks_per_device','5','每个设备最大任务数','2025-07-03 00:38:36','2025-07-03 00:38:36'),(9,1,'notification.email_enabled','false','启用邮件通知','2025-07-03 00:38:36','2025-07-03 00:38:36'),(10,1,'notification.email','','通知邮箱','2025-07-03 00:38:36','2025-07-03 00:38:36'),(11,1,'notification.task_completion','false','任务完成通知','2025-07-03 00:38:36','2025-07-03 00:38:36'),(12,1,'notification.device_offline','false','设备离线通知','2025-07-03 00:38:36','2025-07-03 00:38:36'),(13,1,'notification.error_threshold','10','错误阈值通知','2025-07-03 00:38:36','2025-07-03 00:38:36'),(44,1,'task.default_priority','medium','默认任务优先级','2025-07-03 08:16:28','2025-07-03 08:40:41'),(45,1,'task.default_target_likes','15','默认目标点赞数','2025-07-03 08:16:28','2025-07-03 08:40:41'),(46,1,'task.default_target_collects','0','默认目标收藏数','2025-07-03 08:16:28','2025-07-03 08:40:41'),(47,1,'task.default_target_comments','0','默认目标评论数','2025-07-03 08:16:28','2025-07-03 08:40:41'),(48,1,'general.app_name','小红书点赞助手','应用名称','2025-07-03 08:16:45','2025-07-03 08:16:45'),(49,1,'general.items_per_page','15','每页显示条目数','2025-07-03 08:16:45','2025-07-03 08:16:45'),(50,2,'general.app_name','小红书点赞助手','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(51,2,'general.items_per_page','20','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(52,2,'task.default_priority','medium','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(53,2,'task.default_target_likes','100','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(54,2,'task.default_target_collects','50','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(55,2,'task.default_target_comments','0','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(56,2,'device.auto_assign','true','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(57,2,'device.max_tasks_per_device','5','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(58,2,'notification.email_enabled','false','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(59,2,'notification.email','','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(60,2,'notification.task_completion','false','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(61,2,'notification.device_offline','false','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(62,2,'notification.error_threshold','10','','2025-07-03 09:55:21','2025-07-03 09:55:21'),(63,1,'link.max_concurrent_operations','3','同一链接最大并发操作数','2025-07-03 11:04:15','2025-07-03 11:04:15'),(64,1,'link.cooldown_seconds','60','链接操作后冷却时间（秒）','2025-07-03 11:04:15','2025-07-03 11:04:15'),(65,1,'link.processing_timeout_seconds','80','链接处理超时时间（秒）','2025-07-03 11:04:15','2025-07-03 11:04:15'),(66,1,'link.max_retries','3','链接操作失败最大重试次数','2025-07-03 11:04:15','2025-07-03 11:04:15'),(67,1,'system.max_concurrent_requests','10','最大并发请求数','2025-07-03 11:28:31','2025-07-03 11:28:31'),(68,1,'system.request_rate_limit','60','每分钟最大请求次数','2025-07-03 11:28:31','2025-07-03 11:28:31'),(69,1,'system.queue_timeout_seconds','30','请求队列超时时间（秒）','2025-07-03 11:28:31','2025-07-03 11:28:31');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` text COMMENT '任务描述',
  `status` enum('active','paused','completed') NOT NULL DEFAULT 'active' COMMENT '任务状态',
  `priority` enum('high','medium','low') NOT NULL DEFAULT 'medium' COMMENT '任务优先级',
  `target_likes` int(11) DEFAULT '0' COMMENT '默认目标点赞数',
  `target_collects` int(11) DEFAULT '0' COMMENT '默认目标收藏数',
  `target_comments` int(11) DEFAULT '0',
  `actual_likes` int(11) DEFAULT '0' COMMENT '实际已操作点赞数',
  `actual_collects` int(11) DEFAULT '0' COMMENT '实际已操作收藏数',
  `actual_comments` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=36 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (35,1,'aaa','','active','medium',15,0,0,14,0,0,'2025-07-03 17:46:17','2025-07-04 19:55:35');
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `token` varchar(100) NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','admin123','active','2025-07-02 09:43:30','2025-07-02 09:43:30'),(2,'user1','user1231','active','2025-07-03 01:35:18','2025-07-03 01:54:47'),(3,'user2','user1232','active','2025-07-03 01:35:18','2025-07-03 01:54:53'),(4,'user3','user1233','active','2025-07-03 01:35:18','2025-07-03 01:55:02');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 13:15:02
