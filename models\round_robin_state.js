const db = require('../utils/db');
const Setting = require('./setting');

class RoundRobinState {
  // 创建表（如果不存在）
  static async createTableIfNotExists() {
    try {
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS \`round_robin_states\` (
          \`id\` int(11) NOT NULL AUTO_INCREMENT,
          \`user_id\` int(11) NOT NULL,
          \`high_priority_counter\` int(11) DEFAULT 0 COMMENT '高优先级计数器',
          \`medium_priority_counter\` int(11) DEFAULT 0 COMMENT '中优先级计数器',
          \`low_priority_counter\` int(11) DEFAULT 0 COMMENT '低优先级计数器',
          \`last_reset_time\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上次重置时间',
          \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (\`id\`),
          UNIQUE KEY \`user_id\` (\`user_id\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='循环分配状态表'
      `;

      await db.query(createTableSQL);
      // console.log('round_robin_states 表检查/创建完成');
      return true;
    } catch (error) {
      console.error('创建 round_robin_states 表失败:', error);
      return false;
    }
  }
  // 获取用户的循环分配状态
  static async getUserState(userId) {
    try {
      // 确保表存在
      await this.createTableIfNotExists();
      const [rows] = await db.query(
        'SELECT * FROM round_robin_states WHERE user_id = ?',
        [userId]
      );
      
      if (rows && rows.length > 0) {
        const state = rows[0];
        // 检查是否需要重置计数器
        const resetHours = await Setting.getValue(userId, 'link.round_robin_reset_hours', '24');
        const resetInterval = parseInt(resetHours) * 60 * 60 * 1000; // 转换为毫秒
        
        if (resetInterval > 0) {
          const now = new Date();
          const lastReset = new Date(state.last_reset_time);
          
          if (now - lastReset > resetInterval) {
            console.log(`用户(${userId})循环计数器已超过重置间隔，执行重置`);
            await this.resetCounters(userId);
            return await this.getUserState(userId); // 递归获取重置后的状态
          }
        }
        
        return {
          high_priority_counter: state.high_priority_counter || 0,
          medium_priority_counter: state.medium_priority_counter || 0,
          low_priority_counter: state.low_priority_counter || 0,
          last_reset_time: state.last_reset_time
        };
      } else {
        // 如果不存在，创建初始状态
        await this.initUserState(userId);
        return {
          high_priority_counter: 0,
          medium_priority_counter: 0,
          low_priority_counter: 0,
          last_reset_time: new Date()
        };
      }
    } catch (error) {
      console.error('获取循环分配状态错误:', error);
      throw error;
    }
  }

  // 初始化用户的循环分配状态
  static async initUserState(userId) {
    try {
      const sql = `
        INSERT INTO round_robin_states 
        (user_id, high_priority_counter, medium_priority_counter, low_priority_counter, last_reset_time, created_at, updated_at)
        VALUES (?, 0, 0, 0, NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `;
      
      const [result] = await db.query(sql, [userId]);
      // console.log(`初始化用户(${userId})循环分配状态成功`);
      return result;
    } catch (error) {
      console.error('初始化循环分配状态错误:', error);
      throw error;
    }
  }

  // 更新计数器
  static async updateCounter(userId, priority, increment = 1) {
    try {
      let counterField;
      switch (priority) {
        case 'high':
          counterField = 'high_priority_counter';
          break;
        case 'medium':
          counterField = 'medium_priority_counter';
          break;
        case 'low':
          counterField = 'low_priority_counter';
          break;
        default:
          throw new Error(`无效的优先级: ${priority}`);
      }

      const sql = `
        UPDATE round_robin_states 
        SET ${counterField} = ${counterField} + ?, updated_at = NOW()
        WHERE user_id = ?
      `;
      
      const [result] = await db.query(sql, [increment, userId]);
      
      if (result.affectedRows === 0) {
        // 如果没有更新任何行，说明状态不存在，先初始化
        await this.initUserState(userId);
        await db.query(sql, [increment, userId]);
      }
      
      // console.log(`更新用户(${userId})${priority}优先级计数器 +${increment}`);
      return result;
    } catch (error) {
      console.error('更新循环计数器错误:', error);
      throw error;
    }
  }

  // 重置所有计数器
  static async resetCounters(userId) {
    try {
      const sql = `
        UPDATE round_robin_states 
        SET high_priority_counter = 0, 
            medium_priority_counter = 0, 
            low_priority_counter = 0,
            last_reset_time = NOW(),
            updated_at = NOW()
        WHERE user_id = ?
      `;
      
      const [result] = await db.query(sql, [userId]);
      
      if (result.affectedRows === 0) {
        // 如果没有更新任何行，说明状态不存在，先初始化
        await this.initUserState(userId);
      }
      
      console.log(`重置用户(${userId})所有循环计数器`);
      return result;
    } catch (error) {
      console.error('重置循环计数器错误:', error);
      throw error;
    }
  }

  // 获取下一个循环索引
  static async getNextRoundRobinIndex(userId, priority, totalCount) {
    try {
      if (totalCount === 0) {
        return -1;
      }

      const state = await this.getUserState(userId);
      let currentCounter;
      
      switch (priority) {
        case 'high':
          currentCounter = state.high_priority_counter;
          break;
        case 'medium':
          currentCounter = state.medium_priority_counter;
          break;
        case 'low':
          currentCounter = state.low_priority_counter;
          break;
        default:
          throw new Error(`无效的优先级: ${priority}`);
      }

      // 计算循环索引
      const index = currentCounter % totalCount;
      
      // 更新计数器
      await this.updateCounter(userId, priority, 1);
      
      // console.log(`用户(${userId})${priority}优先级循环索引: ${index}/${totalCount-1} (计数器: ${currentCounter})`);
      return index;
    } catch (error) {
      console.error('获取循环索引错误:', error);
      throw error;
    }
  }

  // 清理过期的状态记录
  static async cleanupExpiredStates(daysOld = 30) {
    try {
      const sql = `
        DELETE FROM round_robin_states 
        WHERE updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `;
      
      const [result] = await db.query(sql, [daysOld]);
      console.log(`清理了 ${result.affectedRows} 个过期的循环分配状态记录`);
      return result.affectedRows;
    } catch (error) {
      console.error('清理过期状态记录错误:', error);
      throw error;
    }
  }
}

module.exports = RoundRobinState;
