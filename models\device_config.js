const db = require('../utils/db');

class DeviceConfig {
  // 获取用户的所有配置
  static async getAllByUserId(userId) {
    try {
      console.log('获取用户配置 - 用户ID:', userId);
      const sql = 'SELECT * FROM device_configs WHERE user_id = ? ORDER BY is_default DESC, name ASC';
      const [rows] = await db.query(sql, [userId]);
      console.log('查询结果行数:', rows.length);
      
      // 确保config字段是对象而不是字符串
      const results = rows.map(row => {
        try {
          if (row.config && typeof row.config === 'string') {
            row.config = JSON.parse(row.config);
          }
        } catch (e) {
          console.error('解析配置JSON失败:', e);
          row.config = {}; // 如果解析失败，使用空对象
        }
        return row;
      });
      
      return results;
    } catch (error) {
      console.error('获取设备配置错误:', error);
      throw error;
    }
  }

  // 获取单个配置
  static async getById(id, userId) {
    try {
      console.log('获取配置详情 - ID:', id, '用户ID:', userId);
      const sql = 'SELECT * FROM device_configs WHERE id = ? AND user_id = ?';
      const [rows] = await db.query(sql, [id, userId]);
      
      if (rows.length === 0) {
        console.log('未找到配置');
        return null;
      }
      
      // 解析配置JSON
      try {
        if (rows[0].config && typeof rows[0].config === 'string') {
          rows[0].config = JSON.parse(rows[0].config);
        }
      } catch (e) {
        console.error('解析配置JSON失败:', e);
        rows[0].config = {}; // 如果解析失败，使用空对象
      }
      
      console.log('找到配置:', rows[0].name);
      return rows[0];
    } catch (error) {
      console.error(`获取设备配置错误 (ID: ${id}):`, error);
      throw error;
    }
  }

  // 获取用户的默认配置
  static async getDefaultByUserId(userId) {
    try {
      // console.log('获取默认配置 - 用户ID:', userId);
      const sql = 'SELECT * FROM device_configs WHERE user_id = ? AND is_default = 1';
      const [rows] = await db.query(sql, [userId]);
      
      let results = [];
      
      if (rows.length > 0) {
        // console.log('找到默认配置:', rows.length, '条');
        results = rows;
      } else {
        console.log('未找到默认配置，返回所有配置');
        // 如果没有默认配置，返回所有配置
        const sql = 'SELECT * FROM device_configs WHERE user_id = ? ORDER BY name ASC';
        const [allRows] = await db.query(sql, [userId]);
        results = allRows;
      }
      
      // 确保config字段是对象而不是字符串
      return results.map(row => {
        try {
          if (row.config && typeof row.config === 'string') {
            row.config = JSON.parse(row.config);
          }
        } catch (e) {
          console.error('解析配置JSON失败:', e);
          row.config = {}; // 如果解析失败，使用空对象
        }
        return row;
      });
    } catch (error) {
      console.error(`获取默认设备配置错误:`, error);
      throw error;
    }
  }

  // 创建新配置
  static async create(configData) {
    try {
      const { user_id, name, config, description, is_default } = configData;
      
      // 如果新配置是默认配置，先取消其他默认配置
      if (is_default) {
        await db.query('UPDATE device_configs SET is_default = 0 WHERE user_id = ?', [user_id]);
      }
      
      const sql = 'INSERT INTO device_configs (user_id, name, config, description, is_default) VALUES (?, ?, ?, ?, ?)';
      const [result] = await db.query(sql, [
        user_id, 
        name, 
        JSON.stringify(config), 
        description || '', 
        is_default ? 1 : 0
      ]);
      
      return result.insertId;
    } catch (error) {
      console.error('创建设备配置错误:', error);
      throw error;
    }
  }

  // 更新配置
  static async update(id, userId, configData) {
    try {
      const { name, config, description, is_default } = configData;
      
      // 如果更新后的配置是默认配置，先取消其他默认配置
      if (is_default) {
        await db.query('UPDATE device_configs SET is_default = 0 WHERE user_id = ?', [userId]);
      }
      
      const sql = 'UPDATE device_configs SET name = ?, config = ?, description = ?, is_default = ? WHERE id = ? AND user_id = ?';
      const [result] = await db.query(sql, [
        name, 
        JSON.stringify(config), 
        description || '', 
        is_default ? 1 : 0,
        id,
        userId
      ]);
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新设备配置错误 (ID: ${id}):`, error);
      throw error;
    }
  }

  // 删除配置
  static async delete(id, userId) {
    try {
      // 检查是否是默认配置
      const config = await this.getById(id, userId);
      
      if (!config) {
        throw new Error('配置不存在或无权限删除');
      }
      
      const sql = 'DELETE FROM device_configs WHERE id = ? AND user_id = ?';
      const [result] = await db.query(sql, [id, userId]);
      
      // 如果删除的是默认配置，且还有其他配置，则设置第一个配置为默认
      if (config.is_default) {
        const [remainingConfigs] = await db.query(
          'SELECT id FROM device_configs WHERE user_id = ? ORDER BY id ASC LIMIT 1',
          [userId]
        );
        
        if (remainingConfigs.length > 0) {
          await db.query(
            'UPDATE device_configs SET is_default = 1 WHERE id = ?',
            [remainingConfigs[0].id]
          );
        }
      }
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除设备配置错误 (ID: ${id}):`, error);
      throw error;
    }
  }
}

module.exports = DeviceConfig; 