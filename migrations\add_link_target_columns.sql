-- 添加链接表中的单独目标字段
ALTER TABLE links 
ADD COLUMN like_target INT NOT NULL DEFAULT 0 COMMENT '链接点赞目标数' AFTER target_likes,
ADD COLUMN collect_target INT NOT NULL DEFAULT 0 COMMENT '链接收藏目标数' AFTER target_collects,
ADD COLUMN comment_target INT NOT NULL DEFAULT 0 COMMENT '链接评论目标数' AFTER target_comments;

-- 将现有的target_likes/collects/comments值复制到新字段中
UPDATE links SET 
like_target = target_likes,
collect_target = target_collects,
comment_target = target_comments
WHERE 1;

-- 添加索引以提高查询性能
ALTER TABLE links
ADD INDEX idx_link_targets (like_target, collect_target, comment_target);